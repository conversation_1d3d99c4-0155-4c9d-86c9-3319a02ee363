I need you to create a complete task feature module following the exact same patterns and structure as the existing leave-request module in `src/modules/leave-request/*`. 

**Prerequisites:**
1. First examine the existing leave-request module structure in `src/modules/leave-request/*` to understand the current patterns
2. Review the TypeScript types in `src/shared/types/api.ts` and `src/shared/types/schema.d.ts` to identify task-related types and backend payload expectations
3. Use the leave-request module as the primary reference template for CRUD operations (as mentioned in previous conversations)

**Implementation Requirements:**

1. **PageList Component** (`src/modules/task/PageList.tsx`):
   - Replicate the exact datatable structure and configuration from leave-request
   - Include column definitions with proper sorting capabilities
   - Implement pagination handling with the same UI patterns
   - Add filter functionality matching the existing implementation
   - Include bulk action handling specifically for export data functionality
   - Provide row actions for view and edit operations (explicitly exclude delete functionality)
   - Ensure proper TypeScript typing using task-related types from the schema files

2. **Create and Edit Pages** (`src/modules/task/CreatePage.tsx` and `src/modules/task/EditPage.tsx`):
   - Follow the identical form structure and UI layout as leave-request create/edit pages
   - Implement validation patterns using validation schemas from `src/shared/types/schema.d.ts`
   - Ensure the form payload matches exactly what the backend expects for task endpoints
   - Maintain consistent error handling and user feedback patterns

3. **API Integration**:
   - Create CRUD operation functions following the same patterns as leave-request
   - Implement custom hooks for data fetching and mutations
   - Use the task-related API endpoints and types identified in `src/shared/types/api.ts`
   - Ensure proper error handling and loading states

4. **Routing and Navigation**:
   - Implement the same routing structure for list, create, and update pages
   - Create route definitions but do NOT add them to `App.tsx` (routes should be ready for integration but not automatically included)
   - Follow the same URL pattern conventions as the leave-request module

5. **State Management**:
   - Replicate the exact state management patterns used in leave-request
   - Maintain consistency in how data is stored, updated, and accessed
   - Use the same patterns for form state, loading states, and error handling

**Quality Assurance:**
- Maintain strict consistency in naming conventions (use "task" instead of "leave-request" throughout)
- Follow the exact file structure and organization patterns
- Ensure all components are properly typed using the task-related TypeScript definitions
- The final implementation should be functionally identical to leave-request but operate on task data

**Deliverable:**
A complete task module in `src/modules/task/` that looks and behaves exactly like the leave-request module but works with task data instead, ready for integration into the application routing.