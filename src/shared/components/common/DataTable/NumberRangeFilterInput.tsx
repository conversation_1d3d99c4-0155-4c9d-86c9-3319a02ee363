import { Stack, TextField } from "@mui/material";

export const NumberRangeFilterInput: React.FC<{
	min: number | null;
	max: number | null;
	setRangeFilter: (minValue: any, maxValue: any, columnId: string) => void;
	columnId: string;
}> = ({ min, max, setRangeFilter, columnId }) => {
	const handleMinChange = (value: string) => {
		const numValue = value === "" ? null : Number(value);
		setRangeFilter(numValue, max, columnId);
	};

	const handleMaxChange = (value: string) => {
		const numValue = value === "" ? null : Number(value);
		setRangeFilter(min, numValue, columnId);
	};

	return (
		<Stack direction="row" spacing={2}>
			<TextField
				type="number"
				size="small"
				variant="outlined"
				value={min ?? ""}
				onChange={(e) => handleMinChange(e.target.value)}
				placeholder="Min"
			/>
			<TextField
				type="number"
				size="small"
				variant="outlined"
				value={max ?? ""}
				onChange={(e) => handleMaxChange(e.target.value)}
				placeholder="Max"
			/>
		</Stack>
	);
};
