import { Inbox as InboxIcon } from "@mui/icons-material";
import {
	<PERSON><PERSON>,
	<PERSON>,
	Button,
	Checkbox,
	CircularProgress,
	Table as MuiTable,
	Stack,
	TableBody,
	TableCell,
	TableContainer,
	TableHead,
	TablePagination,
	TableRow,
	Typography,
} from "@mui/material";
import {
	type Column,
	type ColumnDef,
	flexRender,
	getCoreRowModel,
	getExpandedRowModel,
	getFilteredRowModel,
	getSortedRowModel,
	type Row,
	type SortingState,
	useReactTable,
} from "@tanstack/react-table";
import {
	Fragment,
	memo,
	useCallback,
	useEffect,
	useMemo,
	useState,
} from "react";
import { useNavigate, useSearchParams } from "react-router-dom";
import type { BasePaginationResponse } from "@/shared/types/api";
import type {
	CustomColumnFiltersState,
	SelectionInfo,
	SelectionState,
} from "@/shared/types/common";
import { buildQueryParams, parseQueryParams } from "@/shared/utils/queryParams";
import { ColumnFilterInput } from "./ColumnFilterInput";
import { PaginationActions } from "./PaginationActions";

export interface DataTableProps<TData> {
	columns: ColumnDef<TData, any>[];
	fetchData: (params: URLSearchParams) => Promise<BasePaginationResponse>;
	toolbar?: ({
		globalFilterValue,
		setGlobalFilterValue,
		selectionInfo,
		onSelectAll,
		onDeselectAll,
		hideableColumns,
	}: {
		globalFilterValue: string;
		setGlobalFilterValue: (v: string) => void;
		selectionInfo: SelectionInfo;
		onSelectAll: () => void;
		onDeselectAll: () => void;
		hideableColumns: Column<TData>[];
	}) => React.ReactNode;
	renderRowActions?: (row: Row<TData>) => React.ReactNode;
	renderSubComponent?: (row: Row<TData>) => React.ReactNode;
	enableSelection?: boolean;
	globalSearchEnabled?: boolean;
	onSelectionChange?: (selectionData: {
		selectedIds: string[];
		excludedIds: string[];
		isSelectAll: boolean;
		selectionInfo: SelectionInfo;
		getCurrentFilters: () => {
			sorting: SortingState;
			columnFilters: CustomColumnFiltersState;
			globalFilter: string;
			pagination: { pageIndex: number; pageSize: number };
		};
	}) => void;
}

// ✅ Memoized checkbox components
const SelectionCheckbox = memo(
	({
		isSelected,
		onToggle,
		id,
	}: {
		isSelected: boolean;
		onToggle: (id: string, selected: boolean) => void;
		id: string;
	}) => {
		const handleChange = useCallback(
			(e: React.ChangeEvent<HTMLInputElement>) => {
				onToggle(id, e.target.checked);
			},
			[id, onToggle],
		);

		return <Checkbox checked={isSelected} onChange={handleChange} />;
	},
);

const HeaderCheckbox = memo(
	({
		isFullySelected,
		isPartiallySelected,
		onToggle,
	}: {
		isFullySelected: boolean;
		isPartiallySelected: boolean;
		onToggle: (selected: boolean) => void;
	}) => {
		const handleChange = useCallback(
			(e: React.ChangeEvent<HTMLInputElement>) => {
				onToggle(e.target.checked);
			},
			[onToggle],
		);

		return (
			<Checkbox
				checked={isFullySelected}
				indeterminate={isPartiallySelected}
				onChange={handleChange}
			/>
		);
	},
);

// ✅ Stable selection state helper
const createSelectionState = (
	isSelectAll: boolean,
	selectedIds: Set<string>,
	excludedIds: Set<string>,
): SelectionState => ({ isSelectAll, selectedIds, excludedIds });

export function DataTable<TData>({
	columns: inputColumns,
	fetchData,
	toolbar,
	renderRowActions,
	renderSubComponent,
	enableSelection = false,
	onSelectionChange,
}: DataTableProps<TData>) {
	const navigate = useNavigate();
	const [searchParams] = useSearchParams();
	const initial = useMemo(() => parseQueryParams(searchParams), [searchParams]);

	const [data, setData] = useState<TData[]>([]);
	const [totalItems, setTotalItems] = useState(0);
	const [loading, setLoading] = useState(false);

	// ✅ Stable selection state
	const [selectionState, setSelectionState] = useState<SelectionState>(() =>
		createSelectionState(false, new Set(), new Set()),
	);

	// const [rowSelection, setRowSelection] = useState<RowSelectionState>({});

	const [pagination, setPagination] = useState({
		pageIndex: initial.pageIndex,
		pageSize: initial.pageSize,
	});
	const [sorting, setSorting] = useState<SortingState>(initial.sorting);
	const [columnFilters, setColumnFilters] = useState<CustomColumnFiltersState>(
		initial.columnFilters,
	);
	const [globalFilter, setGlobalFilter] = useState(initial.globalFilter ?? "");

	// ✅ Memoized current page IDs
	const currentPageIds = useMemo(
		() => data.map((item) => (item as any)?.id as string),
		[data],
	);

	// ✅ Stable selection info calculation
	const selectionInfo: SelectionInfo = useMemo(() => {
		if (selectionState.isSelectAll) {
			return {
				selectedCount: totalItems - selectionState.excludedIds.size,
				totalCount: totalItems,
				isSelectAll: true,
			};
		}
		return {
			selectedCount: selectionState.selectedIds.size,
			totalCount: totalItems,
			isSelectAll: false,
		};
	}, [
		selectionState.isSelectAll,
		selectionState.selectedIds.size,
		selectionState.excludedIds.size,
		totalItems,
	]);

	// ✅ Stable selection status calculations
	const { isPageFullySelected, isPagePartiallySelected } = useMemo(() => {
		const selectedInPage = currentPageIds.filter((id) => {
			if (selectionState.isSelectAll) {
				return !selectionState.excludedIds.has(id);
			}
			return selectionState.selectedIds.has(id);
		});

		return {
			isPageFullySelected:
				currentPageIds.length > 0 &&
				selectedInPage.length === currentPageIds.length,
			isPagePartiallySelected:
				selectedInPage.length > 0 &&
				selectedInPage.length < currentPageIds.length,
		};
	}, [currentPageIds, selectionState]);

	// ✅ Stable callbacks with useCallback
	const handleRowToggle = useCallback((rowId: string, selected: boolean) => {
		setSelectionState((prev) => {
			if (prev.isSelectAll) {
				if (selected) {
					const newExcludedIds = new Set(prev.excludedIds);
					newExcludedIds.delete(rowId);
					return createSelectionState(
						prev.isSelectAll,
						prev.selectedIds,
						newExcludedIds,
					);
				}
				const newExcludedIds = new Set(prev.excludedIds);
				newExcludedIds.add(rowId);
				return createSelectionState(
					prev.isSelectAll,
					prev.selectedIds,
					newExcludedIds,
				);
			}
			if (selected) {
				const newSelectedIds = new Set(prev.selectedIds);
				newSelectedIds.add(rowId);
				return createSelectionState(
					prev.isSelectAll,
					newSelectedIds,
					prev.excludedIds,
				);
			}
			const newSelectedIds = new Set(prev.selectedIds);
			newSelectedIds.delete(rowId);
			return createSelectionState(
				prev.isSelectAll,
				newSelectedIds,
				prev.excludedIds,
			);
		});
	}, []);

	const handleSelectAllPage = useCallback(
		(selected: boolean) => {
			setSelectionState((prev) => {
				if (prev.isSelectAll) {
					const newExcludedIds = new Set(prev.excludedIds);
					if (selected) {
						currentPageIds.forEach((id) => newExcludedIds.delete(id));
					} else {
						currentPageIds.forEach((id) => newExcludedIds.add(id));
					}
					return createSelectionState(
						prev.isSelectAll,
						prev.selectedIds,
						newExcludedIds,
					);
				}
				const newSelectedIds = new Set(prev.selectedIds);
				if (selected) {
					currentPageIds.forEach((id) => newSelectedIds.add(id));
				} else {
					currentPageIds.forEach((id) => newSelectedIds.delete(id));
				}
				return createSelectionState(
					prev.isSelectAll,
					newSelectedIds,
					prev.excludedIds,
				);
			});
		},
		[currentPageIds],
	);

	const handleSelectAll = useCallback(() => {
		setSelectionState(createSelectionState(true, new Set(), new Set()));
	}, []);

	const handleDeselectAll = useCallback(() => {
		setSelectionState(createSelectionState(false, new Set(), new Set()));
	}, []);

	const getCurrentFilters = useCallback(
		() => ({
			sorting,
			columnFilters,
			globalFilter,
			pagination,
		}),
		[sorting, columnFilters, globalFilter, pagination],
	);

	// ✅ Stable columns memoization - separated selection logic
	const baseColumns = useMemo(() => {
		const cols: ColumnDef<TData, any>[] = [...inputColumns];

		if (renderRowActions) {
			cols.push({
				id: "actions",
				header: "Actions",
				cell: ({ row }: { row: Row<TData> }) => renderRowActions(row),
			});
		}

		return cols;
	}, [inputColumns, renderRowActions]);

	// ✅ Selection column is separate to avoid re-creating base columns
	const selectionColumn = useMemo(() => {
		if (!enableSelection) return null;

		return {
			id: "select",
			header: () => (
				<HeaderCheckbox
					isFullySelected={isPageFullySelected}
					isPartiallySelected={isPagePartiallySelected}
					onToggle={handleSelectAllPage}
				/>
			),
			cell: ({ row }: { row: Row<TData> }) => {
				const id = (row.original as any)?.id as string;
				const isSelected = selectionState.isSelectAll
					? !selectionState.excludedIds.has(id)
					: selectionState.selectedIds.has(id);

				return (
					<SelectionCheckbox
						id={id}
						isSelected={isSelected}
						onToggle={handleRowToggle}
					/>
				);
			},
			enableSorting: false,
			enableColumnFilter: false,
		};
	}, [
		enableSelection,
		isPageFullySelected,
		isPagePartiallySelected,
		handleSelectAllPage,
		selectionState.isSelectAll,
		selectionState.excludedIds,
		selectionState.selectedIds,
		handleRowToggle,
	]);

	// ✅ Final columns combination
	const columns = useMemo(() => {
		const cols: ColumnDef<TData, any>[] = [];

		if (selectionColumn) {
			cols.push(selectionColumn);
		}

		cols.push(...baseColumns);

		return cols;
	}, [selectionColumn, baseColumns]);

	const table = useReactTable({
		data,
		columns,
		pageCount: Math.ceil(totalItems / pagination.pageSize),
		getRowId: (row) => (row as any)?.id as string,
		state: {
			pagination,
			sorting,
			columnFilters,
			// rowSelection,
		},
		onPaginationChange: setPagination,
		onSortingChange: setSorting,
		onColumnFiltersChange: (updater) => {
			setColumnFilters(updater as CustomColumnFiltersState);
		},
		// onRowSelectionChange: setRowSelection,
		manualPagination: true,
		manualSorting: true,
		manualFiltering: true,
		enableRowSelection: enableSelection,
		getCoreRowModel: getCoreRowModel(),
		getSortedRowModel: getSortedRowModel(),
		getExpandedRowModel: getExpandedRowModel(),
		getFilteredRowModel: getFilteredRowModel(),
	});

	const hideableColumns = useMemo(() => {
		return table
			.getAllColumns()
			.filter(
				(column) =>
					typeof column.accessorFn !== "undefined" && column.getCanHide(),
			);
	}, [table]);

	// ✅ Stable data fetching
	useEffect(() => {
		const params = buildQueryParams({
			pageIndex: pagination.pageIndex,
			pageSize: pagination.pageSize,
			sorting,
			columnFilters,
			globalFilter,
		});
		navigate({ search: params.toString() }, { replace: true });

		const load = async () => {
			setLoading(true);
			try {
				const res = await fetchData(params);
				setData(res.data as TData[]);
				setTotalItems(res.meta.pagination.totalItems);
			} finally {
				setLoading(false);
			}
		};

		load();
	}, [pagination, sorting, columnFilters, globalFilter, fetchData, navigate]);

	// ✅ Stable selection change callback
	useEffect(() => {
		if (enableSelection && onSelectionChange) {
			onSelectionChange({
				selectedIds: Array.from(selectionState.selectedIds),
				excludedIds: Array.from(selectionState.excludedIds),
				isSelectAll: selectionState.isSelectAll,
				selectionInfo,
				getCurrentFilters,
			});
		}
	}, [
		enableSelection,
		onSelectionChange,
		selectionState,
		selectionInfo,
		getCurrentFilters,
	]);

	// Reset selection when filters change
	// biome-ignore lint/correctness/useExhaustiveDependencies: ""
	useEffect(() => {
		handleDeselectAll();
	}, [sorting, columnFilters, globalFilter, handleDeselectAll]);

	return (
		<Box>
			{toolbar?.({
				globalFilterValue: globalFilter,
				setGlobalFilterValue: setGlobalFilter,
				selectionInfo,
				onSelectAll: handleSelectAll,
				onDeselectAll: handleDeselectAll,
				hideableColumns,
			})}

			{table.getState().columnFilters.length > 0 && (
				<Box
					sx={{
						display: "flex",
						flexWrap: "wrap",
						alignItems: "center",
						gap: 1,
						mb: 2,
						px: 2,
					}}
				>
					{(table.getState().columnFilters as CustomColumnFiltersState).map(
						(filter) => {
							const col = table.getColumn(filter.id);
							const meta = col?.columnDef.meta as { columnLabel: string };
							const label = meta?.columnLabel ?? filter.id;
							const operator = filter.value.operator;
							const value = filter.value.value;

							const displayValue = Array.isArray(value)
								? value.join(", ")
								: value?.toString();

							return (
								<Box
									key={filter.id}
									sx={{
										display: "inline-flex",
										alignItems: "center",
										backgroundColor: "#f1f1f1",
										borderRadius: 2,
										px: 1.5,
										py: 0.5,
										fontSize: 14,
									}}
								>
									<b>{label}</b>: {displayValue} ({operator})
								</Box>
							);
						},
					)}

					<Button
						size="small"
						onClick={() => table.resetColumnFilters()}
						sx={{ ml: 1 }}
					>
						Reset Filter
					</Button>
				</Box>
			)}

			{/* Selection Info & Actions */}
			{selectionInfo.selectedCount > 0 && (
				<Alert
					severity="info"
					sx={{
						display: "flex",
						alignItems: "center",
						justifyContent: "space-between",
						"& .MuiAlert-message": {
							flex: 1,
							display: "flex",
							alignItems: "center",
							justifyContent: "space-between",
						},
						mb: 1,
					}}
				>
					<Box>
						<Typography variant="body2" fontWeight={600}>
							{selectionInfo.selectedCount} dari {selectionInfo.totalCount}{" "}
							baris dipilih
							{selectionInfo.isSelectAll && " (Semua data dipilih)"}
						</Typography>

						{!selectionInfo.isSelectAll &&
							selectionInfo.selectedCount < selectionInfo.totalCount && (
								<Typography variant="body2" sx={{ mt: 0.5 }}>
									Ingin memilih semua {selectionInfo.totalCount} data?{" "}
									<Button
										size="small"
										onClick={handleSelectAll}
										sx={{
											textTransform: "none",
											p: 0,
											minWidth: "auto",
										}}
									>
										Pilih Semua Data
									</Button>
								</Typography>
							)}
					</Box>

					<Stack direction="row" spacing={1} sx={{ ml: 2 }}>
						<Button
							size="small"
							onClick={handleDeselectAll}
							variant="outlined"
							color="inherit"
						>
							Batal Pilih
						</Button>
					</Stack>
				</Alert>
			)}

			<TableContainer
				sx={{
					minWidth: 650,
					backgroundColor: "white",
					borderRadius: "8px 8px 0 0",
					overflow: "auto",
				}}
			>
				<MuiTable>
					<TableHead>
						{table.getHeaderGroups().map((headerGroup) => {
							const hasAnyFilter = headerGroup.headers.some((header) =>
								header.column.getCanFilter(),
							);

							return (
								<Fragment key={headerGroup.id}>
									<TableRow>
										{headerGroup.headers.map((header) => (
											<TableCell
												key={header.id}
												sx={{
													backgroundColor: "#F8F9FA",
													px: 3,
													py: 2,
													fontWeight: 700,
												}}
											>
												{flexRender(
													header.column.columnDef.header,
													header.getContext(),
												)}
											</TableCell>
										))}
									</TableRow>

									{hasAnyFilter && (
										<TableRow>
											{headerGroup.headers.map((header) => (
												<TableCell
													key={`${header.id}-filter`}
													sx={{ borderBottom: "1px solid #e0e0e0" }}
												>
													{header.column.getCanFilter() && (
														<ColumnFilterInput
															column={header.column}
															table={table}
														/>
													)}
												</TableCell>
											))}
										</TableRow>
									)}
								</Fragment>
							);
						})}
					</TableHead>

					<TableBody>
						{loading ? (
							<TableRow>
								<TableCell colSpan={columns.length} align="center">
									<Box py={4}>
										<CircularProgress />
									</Box>
								</TableCell>
							</TableRow>
						) : table.getRowModel().rows.length === 0 ? (
							<TableRow>
								<TableCell colSpan={columns.length} align="center">
									<Box
										py={6}
										display="flex"
										flexDirection="column"
										alignItems="center"
										color="text.secondary"
									>
										<InboxIcon
											sx={{ fontSize: 48, mb: 2, color: "grey.400" }}
										/>
										<Typography variant="h6" gutterBottom>
											Tidak ada data
										</Typography>
										<Typography variant="body2">
											Coba ubah filter pencarian atau tambahkan data baru.
										</Typography>
									</Box>
								</TableCell>
							</TableRow>
						) : (
							table.getRowModel().rows.map((row) => (
								<Fragment key={row.id}>
									<TableRow
										sx={{
											"&:hover": {
												backgroundColor: "#f0f0f0 !important",
											},
										}}
									>
										{row.getVisibleCells().map((cell) => (
											<TableCell
												key={cell.id}
												sx={{ borderBottom: "1px solid #e0e0e0" }}
											>
												{flexRender(
													cell.column.columnDef.cell,
													cell.getContext(),
												)}
											</TableCell>
										))}
									</TableRow>

									{row.getIsExpanded() && renderSubComponent && (
										<TableRow>
											<TableCell colSpan={columns.length}>
												{renderSubComponent(row)}
											</TableCell>
										</TableRow>
									)}
								</Fragment>
							))
						)}
					</TableBody>
				</MuiTable>
			</TableContainer>

			<TablePagination
				component="div"
				count={totalItems}
				page={pagination.pageIndex}
				rowsPerPage={pagination.pageSize}
				onPageChange={(_, newPage) => table.setPageIndex(newPage)}
				onRowsPerPageChange={(e) => table.setPageSize(Number(e.target.value))}
				rowsPerPageOptions={[5, 10, 25, 50, 100, 300, 500]}
				ActionsComponent={PaginationActions}
				labelRowsPerPage="Data per halaman"
				labelDisplayedRows={({ from, to, count }) =>
					`${from}–${to} dari ${count !== -1 ? count : `lebih dari ${to}`}`
				}
				slotProps={{
					select: {
						inputProps: {
							"aria-label": "rows per page",
						},
					},
					toolbar: {
						sx: {
							minHeight: {
								xs: "80px",
							},
						},
					},
				}}
				sx={{ width: "100%", backgroundColor: "#F8F9FA" }}
			/>
		</Box>
	);
}
