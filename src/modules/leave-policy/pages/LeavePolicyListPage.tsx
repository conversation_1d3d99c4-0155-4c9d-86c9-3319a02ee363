import { <PERSON>, But<PERSON>, <PERSON>, Stack, Typography } from "@mui/material";
import type { ColumnDef } from "@tanstack/react-table";
import { useState } from "react";
import { Link as ReactRouterLink, useNavigate } from "react-router";
import IconsaxAddSquareIcon from "@/assets/icons/iconsax-add-square.svg?react";
import IconsaxReceiveSquareIcon from "@/assets/icons/iconsax-receive-square.svg?react";
import { DataTable } from "@/shared/components/common/DataTable";
import { ColumnHeader } from "@/shared/components/common/DataTable/ColumnHeader";
import { RowActions } from "@/shared/components/common/DataTable/RowActions";
import { ViewOptions } from "@/shared/components/common/DataTable/ViewOption";
import { Footer } from "@/shared/components/common/Footer";
import { IconWrapper } from "@/shared/components/common/IconWrapper";
import { useBulkActionHandler } from "@/shared/hooks/useBulkActionHandler";
import { useMutationCallbacks } from "@/shared/hooks/useMutationCallback";
import type { LeavePolicyResponse } from "@/shared/types/api";
import type { SelectionData } from "@/shared/types/common";
import { getAllLeavePolicy } from "../api/getAllLeavePolicy";
import { useBulkActionLeavePolicy } from "../hooks/useBulkActionLeavePolicy";

const LeavePolicyListPage: React.FC = () => {
	const navigate = useNavigate();
	const [selectionData, setSelectionData] = useState<SelectionData>({
		selectedIds: [],
		excludedIds: [],
		isSelectAll: false,
		selectionInfo: { selectedCount: 0, totalCount: 0, isSelectAll: false },
	});

	const columns: ColumnDef<LeavePolicyResponse>[] = [
		{
			accessorKey: "name",
			header: ({ column }) => (
				<ColumnHeader column={column} title="Nama Kebijakan" />
			),
			cell: (info) => info.getValue(),
			enableColumnFilter: true,
			enableSorting: true,
			meta: { columnLabel: "Nama Kebijakan", filterVariant: "textSearch" },
		},
		{
			accessorKey: "description",
			header: ({ column }) => (
				<ColumnHeader
					sx={{ minWidth: 200 }}
					column={column}
					title="Deskripsi"
				/>
			),
			cell: (info) => info.getValue(),
			enableColumnFilter: false,
			enableSorting: false,
			meta: { columnLabel: "Deskripsi" },
		},
		{
			accessorKey: "quota",
			header: ({ column }) => <ColumnHeader column={column} title="Kuota" />,
			cell: (info) => info.getValue(),
			enableColumnFilter: false,
			enableSorting: true,
			meta: { columnLabel: "Kuota" },
		},
		{
			accessorKey: "isCountedAsPresent",
			header: ({ column }) => (
				<ColumnHeader column={column} title="Dihitung Sebagai Hadir" />
			),
			cell: (info) => {
				const value = info.getValue();
				return (
					<Chip
						label={value ? "Ya" : "Tidak"}
						color={value ? "success" : "default"}
						size="small"
					/>
				);
			},
			enableColumnFilter: true,
			enableSorting: true,
			meta: {
				columnLabel: "Dihitung Sebagai Hadir",
				filterVariant: "select",
				selectOptions: [
					{ label: "Ya", value: "true" },
					{ label: "Tidak", value: "false" },
				],
			},
		},
		{
			accessorKey: "createdAt",
			header: ({ column }) => <ColumnHeader column={column} title="Dibuat" />,
			cell: (info) => {
				const value = info.getValue();
				if (!value) return "-";
				const date = new Date(value as string);
				return (
					<Box sx={{ width: 100 }}>
						{date.toLocaleDateString("id-ID", {
							year: "numeric",
							month: "long",
							day: "numeric",
							hour: "2-digit",
							minute: "2-digit",
						})}
					</Box>
				);
			},
			enableColumnFilter: false,
			meta: { columnLabel: "Dibuat" },
		},
	];

	const { handleSuccess, handleError } = useMutationCallbacks();
	const bulkActionMutation = useBulkActionLeavePolicy({
		onSuccessCallback: (data) => {
			if (data instanceof Blob) {
				// Download CSV
				const url = window.URL.createObjectURL(data);
				const a = document.createElement("a");
				a.href = url;
				a.download = "exported_leave_policies.csv";
				document.body.appendChild(a);
				a.click();
				a.remove();
				window.URL.revokeObjectURL(url);
			} else {
				handleSuccess(data.message);
			}
		},
		onErrorCallback: handleError,
	});

	const handleBulkAction = useBulkActionHandler(
		bulkActionMutation,
		selectionData,
	);

	return (
		<Box sx={{ pb: 10 }}>
			<Typography variant="h6" component="h2" sx={{ mb: 3 }}>
				Kebijakan Cuti
			</Typography>

			<Box
				sx={{
					backgroundColor: "background.paper",
					borderRadius: 2,
					p: 4,
					overflowX: "auto",
				}}
			>
				<DataTable
					columns={columns}
					fetchData={getAllLeavePolicy}
					enableSelection
					onSelectionChange={setSelectionData}
					renderRowActions={(row) => (
						<RowActions
							row={row}
							onEdit={(data: LeavePolicyResponse) =>
								navigate(`/leave-policies/${data.id}/edit`)
							}
							viewTitle="Detail Kebijakan Cuti"
							renderDetail={(data: LeavePolicyResponse) => (
								<Stack direction="column" spacing={2} sx={{ mt: 1 }}>
									<Stack
										direction="column"
										spacing={2}
										sx={{ mt: 1, textAlign: "right" }}
									>
										<Stack direction="row" justifyContent="space-between">
											<Typography color="textDisabled">
												Nama Kebijakan
											</Typography>
											<Typography>{data.name}</Typography>
										</Stack>
										<Stack direction="row" justifyContent="space-between">
											<Typography color="textDisabled">Deskripsi</Typography>
											<Typography>{data.description || "-"}</Typography>
										</Stack>
										<Stack direction="row" justifyContent="space-between">
											<Typography color="textDisabled">Kuota</Typography>
											<Typography>{data.quota}</Typography>
										</Stack>
										<Stack direction="row" justifyContent="space-between">
											<Typography color="textDisabled">
												Dihitung Sebagai Hadir
											</Typography>
											<Typography>
												{data.isCountedAsPresent ? "Ya" : "Tidak"}
											</Typography>
										</Stack>
									</Stack>
								</Stack>
							)}
						/>
					)}
					toolbar={({ hideableColumns, onDeselectAll }) => {
						return (
							<Stack
								direction="row"
								alignItems="center"
								justifyContent="space-between"
								sx={{ mb: 4 }}
							>
								<Box>
									<Typography variant="subtitle1">
										Tabel Manajemen Kebijakan Cuti
									</Typography>
								</Box>
								<Stack direction="row" spacing={2} alignItems="center">
									<Button
										component={ReactRouterLink}
										to="/leave-policies/new"
										variant="contained"
										color="primary"
										startIcon={<IconWrapper icon={IconsaxAddSquareIcon} />}
									>
										Tambah Data
									</Button>
									<Button
										variant="outlined"
										color="inherit"
										startIcon={<IconWrapper icon={IconsaxReceiveSquareIcon} />}
										onClick={() => handleBulkAction("export", onDeselectAll)}
									>
										Export Data
									</Button>
									<ViewOptions hideableColumns={hideableColumns} />
								</Stack>
							</Stack>
						);
					}}
				/>
			</Box>

			<Box sx={{ mt: 4 }}>
				<Footer />
			</Box>
		</Box>
	);
};

export default LeavePolicyListPage;
