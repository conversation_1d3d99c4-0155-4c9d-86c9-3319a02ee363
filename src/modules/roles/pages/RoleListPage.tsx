import { <PERSON>, Button, Stack, Typography } from "@mui/material";
import type { ColumnDef } from "@tanstack/react-table";
import { useState } from "react";
import { Link as ReactRouterLink, useNavigate } from "react-router";
import IconsaxAddSquareIcon from "@/assets/icons/iconsax-add-square.svg?react";
import IconsaxReceiveSquareIcon from "@/assets/icons/iconsax-receive-square.svg?react";
import { DataTable } from "@/shared/components/common/DataTable";
import { ColumnHeader } from "@/shared/components/common/DataTable/ColumnHeader";
import { RowActions } from "@/shared/components/common/DataTable/RowActions";
import { ViewOptions } from "@/shared/components/common/DataTable/ViewOption";
import { Footer } from "@/shared/components/common/Footer";
import { IconWrapper } from "@/shared/components/common/IconWrapper";
import { useBulkActionHandler } from "@/shared/hooks/useBulkActionHandler";
import { useMutationCallbacks } from "@/shared/hooks/useMutationCallback";
import type { Role } from "@/shared/types/api";
import type { SelectionData } from "@/shared/types/common";
import { getAllRole } from "../api/getAllRole";
import { useBulkActionRole } from "../hooks/useBulkActionRole";

const RoleListPage: React.FC = () => {
	const navigate = useNavigate();
	const [selectionData, setSelectionData] = useState<SelectionData>({
		selectedIds: [],
		excludedIds: [],
		isSelectAll: false,
		selectionInfo: { selectedCount: 0, totalCount: 0, isSelectAll: false },
	});

	const { handleSuccess, handleError } = useMutationCallbacks();
	const bulkActionMutation = useBulkActionRole({
		onSuccessCallback: (data) => {
			if (data instanceof Blob) {
				// Download CSV
				const url = window.URL.createObjectURL(data);
				const a = document.createElement("a");
				a.href = url;
				a.download = "exported_roles.csv";
				document.body.appendChild(a);
				a.click();
				a.remove();
				window.URL.revokeObjectURL(url);
			} else {
				handleSuccess(data.message);
			}
		},
		onErrorCallback: handleError,
	});

	const handleBulkAction = useBulkActionHandler(
		bulkActionMutation,
		selectionData,
	);

	const columns: ColumnDef<
		Role & {
			parentName: string | null;
		}
	>[] = [
		{
			accessorKey: "name",
			header: ({ column }) => <ColumnHeader column={column} title="Nama" />,
			cell: (info) => info.getValue(),
			enableColumnFilter: true,
			meta: { columnLabel: "Nama", filterVariant: "textSearch" },
		},
		{
			accessorKey: "parentName",
			header: ({ column }) => <ColumnHeader column={column} title="Atasan" />,
			cell: (info) => {
				const value = info.getValue();

				if (!value) return "-";

				return value;
			},
			enableColumnFilter: false,
			enableSorting: true,
			meta: { columnLabel: "Atasan" },
		},
		{
			accessorKey: "description",
			header: ({ column }) => (
				<ColumnHeader column={column} title="Deskripsi" />
			),
			cell: (info) => info.getValue(),
			enableColumnFilter: false,
			enableSorting: false,
			meta: { columnLabel: "Deskripsi" },
		},
	];

	return (
		<Box sx={{ pb: 10 }}>
			<Typography variant="h6" component="h2" sx={{ mb: 3 }}>
				Manajemen Jabatan
			</Typography>
			<Box
				sx={{
					backgroundColor: "background.paper",
					borderRadius: 2,
					p: 4,
					overflow: "auto",
				}}
			>
				<DataTable
					columns={columns}
					fetchData={getAllRole}
					enableSelection
					onSelectionChange={setSelectionData}
					renderRowActions={(row) => (
						<RowActions
							row={row}
							onEdit={(data) =>
								navigate(`/organizations/${data.id}/edit`)
							}
							viewTitle="Detail Izin Keluar"
							renderDetail={(data) => (
								<>
									<Stack direction="column" spacing={2} sx={{ mt: 1 }}>
										<Stack
											direction="column"
											spacing={2}
											sx={{ mt: 1, textAlign: "right" }}
										>
											<Stack direction="row" justifyContent="space-between">
												<Typography color="textDisabled">
													Nama Jabatan
												</Typography>
												<Typography>{data.name}</Typography>
											</Stack>
											<Stack direction="row" justifyContent="space-between">
												<Typography color="textDisabled">Deskipsi</Typography>
												<Typography>
													{data.description ? data.description : "-"}
												</Typography>
											</Stack>
											<Stack direction="row" justifyContent="space-between">
												<Typography color="textDisabled">Atasan</Typography>
												<Typography>
													{data.parentName ? data.parentName : "-"}
												</Typography>
											</Stack>
										</Stack>
									</Stack>
								</>
							)}
						/>
					)}
					toolbar={({ hideableColumns, onDeselectAll }) => {
						return (
							<Stack
								direction="row"
								alignItems="center"
								justifyContent="space-between"
								sx={{ mb: 4 }}
							>
								<Box>
									<Typography variant="subtitle1">
										Tabel Manajemen Jabatan
									</Typography>
								</Box>
								<Stack direction="row" spacing={2} alignItems="center">
									<Button
										component={ReactRouterLink}
										to="/organizations/new"
										variant="contained"
										color="primary"
										startIcon={<IconWrapper icon={IconsaxAddSquareIcon} />}
									>
										Tambah Data
									</Button>
									<Button
										variant="outlined"
										color="inherit"
										startIcon={<IconWrapper icon={IconsaxReceiveSquareIcon} />}
										onClick={() => handleBulkAction("export", onDeselectAll)}
									>
										Export Data
									</Button>
									<ViewOptions hideableColumns={hideableColumns} />
								</Stack>
							</Stack>
						);
					}}
				/>
			</Box>
			<Box sx={{ mt: 4 }}>
				<Footer />
			</Box>
		</Box>
	);
};

export default RoleListPage;
