import { type UseQueryOptions, useQuery } from "@tanstack/react-query";
import type { AxiosError } from "axios";
import type {
	BaseErrorResponse,
	GetLeavePolicyOptionsResponse,
} from "@/shared/types/api";
import { getLeavePolicyOptions } from "../api/getLeavePolicyOptions";

export const useGetLeavePolicyOptions = (
	options?: Omit<
		UseQueryOptions<
			GetLeavePolicyOptionsResponse,
			AxiosError<BaseErrorResponse>
		>,
		"queryKey" | "queryFn"
	>,
) => {
	return useQuery({
		queryKey: ["getLeavePolicyOptions"],
		queryFn: async () => {
			return getLeavePolicyOptions();
		},
		...options,
	});
};
