import { useGetUserOptions } from "@/modules/users/hooks/useGetUserOptions";
import { FormProvider } from "@/shared/components/common/Form/FormProvider";
import { useMutationCallbacks } from "@/shared/hooks/useMutationCallback";
import type { SelectOption } from "@/shared/types/common";
import { LeaveRequestFormContent } from "../components/LeaveRequestFormContent";
import { useCreateLeaveRequest } from "../hooks/useCreateLeaveRequest";
import { useGetLeavePolicyOptions } from "../hooks/useGetLeavePolicyOptions";
import {
	type CreateLeaveRequestSchema,
	createLeaveRequestSchema,
} from "../validation/createLeaveRequestSchema";

const CreateLeaveRequestPage: React.FC = () => {
	const { data: userOptionsData, isLoading: isUserOptionsLoading } =
		useGetUserOptions();
	const {
		data: leavePolicyOptionsData,
		isLoading: isLeavePolicyOptionsLoading,
	} = useGetLeavePolicyOptions();

	const isLoading = isUserOptionsLoading || isLeavePolicyOptionsLoading;

	const { handleSuccess, handleError } = useMutationCallbacks();
	const mutation = useCreateLeaveRequest({
		onSuccessCallback: (data) => handleSuccess(data.message, "/leaves"),
		onErrorCallback: handleError,
	});

	const onSubmit = (data: CreateLeaveRequestSchema) => {
		const formData = new FormData();
		formData.append("userId", data.userId);
		formData.append("leavePolicyId", data.leavePolicyId);
		formData.append("startDate", data.startDate);
		formData.append("endDate", data.endDate);
		formData.append("description", data.description);

		if (data.document) formData.append("document", data.document);

		mutation.mutate(formData);
	};

	return (
		<FormProvider
			schema={createLeaveRequestSchema}
			defaultValues={{
				userId: "",
				leavePolicyId: "",
				startDate: "",
				endDate: "",
				description: "",
				document: undefined,
			}}
			onSubmit={onSubmit}
		>
			<LeaveRequestFormContent
				label="Buat Permintaan Cuti Baru"
				isLoading={isLoading}
				isSubmitting={mutation.isPending}
				userOptions={userOptionsData?.data as SelectOption[]}
				leavePolicyOptions={leavePolicyOptionsData?.data as SelectOption[]}
			/>
		</FormProvider>
	);
};

export default CreateLeaveRequestPage;
