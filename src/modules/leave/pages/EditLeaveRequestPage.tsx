import { useParams } from "react-router";
import { useGetUserOptions } from "@/modules/users/hooks/useGetUserOptions";
import { FormProvider } from "@/shared/components/common/Form/FormProvider";
import FullPageLoader from "@/shared/components/common/FullPageLoader";
import { useMutationCallbacks } from "@/shared/hooks/useMutationCallback";
import NotFoundPage from "@/shared/pages/404";
import type { SelectOption } from "@/shared/types/common";
import { LeaveRequestFormContent } from "../components/LeaveRequestFormContent";
import { useGetLeavePolicyOptions } from "../hooks/useGetLeavePolicyOptions";
import { useGetLeaveRequest } from "../hooks/useGetLeaveRequest";
import { useUpdateLeaveRequest } from "../hooks/useUpdateLeaveRequest";
import {
	type UpdateLeaveRequestSchema,
	updateLeaveRequestSchema,
} from "../validation/updateLeaveRequestSchema";

const EditLeaveRequestPage: React.FC = () => {
	const leaveRequestId = useParams().leaveRequestId as string;

	const { data: leaveRequestData, isLoading: isLeaveRequestLoading } =
		useGetLeaveRequest(leaveRequestId);
	const { data: userOptionsData, isLoading: isUserOptionsLoading } =
		useGetUserOptions();
	const {
		data: leavePolicyOptionsData,
		isLoading: isLeavePolicyOptionsLoading,
	} = useGetLeavePolicyOptions();

	const isLoading =
		isUserOptionsLoading ||
		isLeaveRequestLoading ||
		isLeavePolicyOptionsLoading;

	const { handleSuccess, handleError } = useMutationCallbacks();
	const mutation = useUpdateLeaveRequest({
		onSuccessCallback: (data) => handleSuccess(data.message, "/leaves"),
		onErrorCallback: handleError,
	});

	const onSubmit = (data: UpdateLeaveRequestSchema) => {
		const formData = new FormData();
		if (data.userId) formData.append("userId", data.userId);
		if (data.leavePolicyId)
			formData.append("leavePolicyId", data.leavePolicyId);
		if (data.startDate) formData.append("startDate", data.startDate);
		if (data.endDate) formData.append("endDate", data.endDate);
		if (data.description) formData.append("description", data.description);
		if (data.document) formData.append("document", data.document);

		mutation.mutate({ leaveRequestId, payload: formData });
	};

	if (isLoading) {
		return <FullPageLoader />;
	}

	if (!isLoading && !leaveRequestData?.data) {
		return <NotFoundPage resourceType="Permintaan Cuti" redirectTo="/leaves" />;
	}

	const defaultValues: UpdateLeaveRequestSchema = {
		userId: leaveRequestData?.data.userId,
		leavePolicyId: leaveRequestData?.data.leavePolicyId,
		startDate: leaveRequestData?.data.startDate,
		endDate: leaveRequestData?.data.endDate,
		description: leaveRequestData?.data.reason,
		document: undefined,
	};

	return (
		<FormProvider
			schema={updateLeaveRequestSchema}
			defaultValues={defaultValues}
			onSubmit={onSubmit}
		>
			<LeaveRequestFormContent
				label="Edit Permintaan Cuti"
				isLoading={false}
				isSubmitting={mutation.isPending}
				userOptions={userOptionsData?.data as SelectOption[]}
				leavePolicyOptions={leavePolicyOptionsData?.data as SelectOption[]}
			/>
		</FormProvider>
	);
};

export default EditLeaveRequestPage;
