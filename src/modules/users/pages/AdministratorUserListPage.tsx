import { <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>ack, Typography } from "@mui/material";
import type { ColumnDef } from "@tanstack/react-table";
import { useState } from "react";
import { Link as ReactRouterLink, useNavigate } from "react-router";
import IconsaxAddSquareIcon from "@/assets/icons/iconsax-add-square.svg?react";
import IconsaxReceiveSquareIcon from "@/assets/icons/iconsax-receive-square.svg?react";
import { DataTable } from "@/shared/components/common/DataTable";
import { ColumnHeader } from "@/shared/components/common/DataTable/ColumnHeader";
import { RowActions } from "@/shared/components/common/DataTable/RowActions";
import { ViewOptions } from "@/shared/components/common/DataTable/ViewOption";
import { Footer } from "@/shared/components/common/Footer";
import { IconWrapper } from "@/shared/components/common/IconWrapper";
import { useBulkActionHandler } from "@/shared/hooks/useBulkActionHandler";
import { useMutationCallbacks } from "@/shared/hooks/useMutationCallback";
import type { AdministratorUser } from "@/shared/types/api";
import type { SelectionData } from "@/shared/types/common";
import { getAllAdministratorUser } from "../api/getAllAdministratorUser";
import { useBulkActionAdministratorUser } from "../hooks/useBulkActionAdministratorUser";

const AdministratorUserListPage: React.FC = () => {
	const navigate = useNavigate();

	const [selectionData, setSelectionData] = useState<SelectionData>({
		selectedIds: [],
		excludedIds: [],
		isSelectAll: false,
		selectionInfo: { selectedCount: 0, totalCount: 0, isSelectAll: false },
	});

	const { handleSuccess, handleError } = useMutationCallbacks();
	const bulkActionMutation = useBulkActionAdministratorUser({
		onSuccessCallback: (data) => {
			if (data instanceof Blob) {
				// Download CSV
				const url = window.URL.createObjectURL(data);
				const a = document.createElement("a");
				a.href = url;
				a.download = "exported_administrator_users.csv";
				document.body.appendChild(a);
				a.click();
				a.remove();
				window.URL.revokeObjectURL(url);
			} else {
				handleSuccess(data.message);
			}
		},
		onErrorCallback: handleError,
	});

	const handleBulkAction = useBulkActionHandler(
		bulkActionMutation,
		selectionData,
	);

	const columns: ColumnDef<
		AdministratorUser & {
			userName: string;
		}
	>[] = [
		{
			accessorKey: "name",
			header: ({ column }) => <ColumnHeader column={column} title="Nama" />,
			cell: (info) => info.getValue(),
			enableColumnFilter: true,
			meta: { columnLabel: "Nama", filterVariant: "textSearch" },
		},
		{
			accessorKey: "email",
			header: ({ column }) => <ColumnHeader column={column} title="Email" />,
			cell: (info) => info.getValue(),
			enableColumnFilter: true,
			meta: { columnLabel: "Email", filterVariant: "textSearch" },
		},
		{
			accessorKey: "userName",
			header: ({ column }) => <ColumnHeader column={column} title="User" />,
			cell: ({ row }) => {
				const value = row.original.userName;

				if (!value) return "-";

				return value;
			},
			enableColumnFilter: false,
			meta: { columnLabel: "User" },
		},
		{
			accessorKey: "image",
			header: ({ column }) => <ColumnHeader column={column} title="Avatar" />,
			cell: ({ row }) => (
				<Avatar alt={row.original.name} src={row.original.image}>
					{row.original.name.charAt(0).toUpperCase() || "U"}
				</Avatar>
			),
			enableColumnFilter: false,
			enableSorting: false,
			meta: { columnLabel: "Avatar" },
		},
		{
			accessorKey: "createdAt",
			header: ({ column }) => <ColumnHeader column={column} title="Dibuat" />,
			cell: (info) => {
				const value = info.getValue();
				if (!value) return "-";
				const date = new Date(value as string);
				return (
					<Box sx={{ width: 100 }}>
						{date.toLocaleDateString("id-ID", {
							year: "numeric",
							month: "long",
							day: "numeric",
							hour: "2-digit",
							minute: "2-digit",
						})}
					</Box>
				);
			},
			enableColumnFilter: false,
			meta: { columnLabel: "Dibuat" },
		},
	];

	return (
		<Box sx={{ pb: 10 }}>
			<Typography variant="h6" component="h2" sx={{ mb: 3 }}>
				Manajemen Administrator User
			</Typography>
			<Box
				sx={{
					backgroundColor: "background.paper",
					borderRadius: 2,
					p: 4,
					overflow: "auto",
				}}
			>
				<DataTable
					columns={columns}
					fetchData={getAllAdministratorUser}
					enableSelection
					onSelectionChange={setSelectionData}
					renderRowActions={(row) => (
						<RowActions
							row={row}
							onEdit={(data) =>
								navigate(`/users/administrators/${data.id}/edit`)
							}
							viewTitle="Detail Absensi"
							renderDetail={(data) => (
								<>
									<Box>
										<Typography variant="body1">Data Pribadi</Typography>
										<Stack
											direction="column"
											spacing={2}
											sx={{ mt: 1, textAlign: "right" }}
										>
											<Stack direction="row" justifyContent="space-between">
												<Typography color="textDisabled">
													Nama kariawan
												</Typography>
												<Typography>{data.name}</Typography>
											</Stack>
											<Stack direction="row" justifyContent="space-between">
												<Typography color="textDisabled">Email</Typography>
												<Typography>{data.email}</Typography>
											</Stack>
											<Stack direction="row" justifyContent="space-between">
												<Typography color="textDisabled">
													Nama Pengguna
												</Typography>
												<Typography>{data.userName}</Typography>
											</Stack>
										</Stack>
									</Box>
								</>
							)}
							drawerSx={{ minWidth: 600 }}
						/>
					)}
					toolbar={({ hideableColumns, onDeselectAll }) => {
						return (
							<Stack direction="column" spacing={2} sx={{ mb: 2 }}>
								<Stack
									direction="row"
									alignItems="center"
									justifyContent="space-between"
								>
									<Box>
										<Typography variant="subtitle1">
											Tabel Manajemen Administrator User
										</Typography>
									</Box>
									<Stack direction="row" spacing={2} alignItems="center">
										<Button
											component={ReactRouterLink}
											to="/users/administrators/new"
											variant="contained"
											color="primary"
											startIcon={<IconWrapper icon={IconsaxAddSquareIcon} />}
										>
											Tambah Data
										</Button>
										<Button
											variant="outlined"
											color="inherit"
											startIcon={
												<IconWrapper icon={IconsaxReceiveSquareIcon} />
											}
											onClick={() => handleBulkAction("export", onDeselectAll)}
										>
											Export Data
										</Button>
										<ViewOptions hideableColumns={hideableColumns} />
									</Stack>
								</Stack>
							</Stack>
						);
					}}
				/>
			</Box>
			<Box sx={{ mt: 4 }}>
				<Footer />
			</Box>
		</Box>
	);
};

export default AdministratorUserListPage;
